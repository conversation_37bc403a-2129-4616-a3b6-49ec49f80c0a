# 🛡️ Scripts de Instalación V2Ray Proxy + Protección Geográfica

Estos scripts automatizan la instalación completa de un proxy V2Ray ofuscado con protección geográfica para servidores IPTV/Xuione.

## 📁 Archivos Incluidos

- `install-v2ray-proxy.sh` - Instalador del proxy V2Ray reverse
- `install-geo-protection.sh` - Instalador de protección geográfica
- `README.md` - Este archivo de instrucciones

## 🚀 Instalación Rápida

### Paso 1: Preparar VPS
```bash
# Conectarse al VPS como root
ssh root@TU_IP_VPS

# Actualizar sistema (opcional, el script lo hace)
apt update && apt upgrade -y
```

### Paso 2: Descargar Scripts
```bash
# Opción A: Descargar directamente
wget https://raw.githubusercontent.com/TU_REPO/install-v2ray-proxy.sh
wget https://raw.githubusercontent.com/TU_REPO/install-geo-protection.sh

# Opción B: Crear manualmente (copiar contenido)
nano install-v2ray-proxy.sh
nano install-geo-protection.sh

# Dar permisos
chmod +x install-v2ray-proxy.sh
chmod +x install-geo-protection.sh
```

### Paso 3: Configurar Variables
```bash
# Editar el script del proxy antes de ejecutar
nano install-v2ray-proxy.sh

# Cambiar estas líneas según tu configuración:
TARGET_IP="**************"        # IP de tu servidor Xuione
CDN_DOMAIN="cdn.rgstreamy.site"   # Tu dominio CDN
```

### Paso 4: Ejecutar Instalación
```bash
# 1. Instalar proxy V2Ray
./install-v2ray-proxy.sh

# 2. Instalar protección geográfica
./install-geo-protection.sh
```

## ⚙️ Configuración Post-Instalación

### DNS del Dominio
Configura tu dominio para que apunte al VPS:
```
Tipo: A
Nombre: cdn.rgstreamy.site
Valor: IP_DE_TU_VPS
TTL: 300
```

### Verificar Funcionamiento
```bash
# Ver estado del proxy
v2ray-status

# Probar proxy XUI
test-xui-proxy

# Ver protección geográfica
monitor-geoblock
```

## 🌍 Países Permitidos

### ✅ Permitidos:
- **LATAM**: Argentina, Bolivia, Brasil, Chile, Colombia, Costa Rica, Cuba, República Dominicana, Ecuador, El Salvador, Guatemala, Honduras, México, Nicaragua, Panamá, Paraguay, Perú, Uruguay, Venezuela, Belice, Guyana, Surinam, Guayana Francesa
- **Norte América**: Estados Unidos, Canadá
- **Europa**: Alemania, Holanda

### 🚫 Bloqueados:
- Resto del mundo (Asia, África, otras partes de Europa, Oceanía)

## 📱 Configuración para Clientes

### URLs para IPTV:
```
# En lugar de:
http://**************/

# Usar:
http://TU_IP_VPS/
http://TU_IP_VPS:2052/
http://cdn.rgstreamy.site/  (si DNS configurado)
```

### Configuración V2Ray:
```json
{
  "v": "2",
  "ps": "V2Ray-Proxy",
  "add": "TU_IP_VPS",
  "port": "80",
  "id": "UUID_GENERADO",
  "aid": "0",
  "net": "ws",
  "type": "none",
  "host": "TU_IP_VPS",
  "path": "/v2ray",
  "tls": ""
}
```

## 🔧 Comandos Útiles

### Gestión del Proxy:
```bash
v2ray-status              # Ver estado completo
test-xui-proxy           # Probar funcionamiento
systemctl restart v2ray nginx  # Reiniciar servicios
```

### Gestión de Protección Geográfica:
```bash
monitor-geoblock         # Ver estadísticas
apply-geoblock          # Aplicar/recargar reglas
setup-geoblock          # Actualizar listas de países
disable-geoblock        # Desactivar temporalmente
```

### Logs y Diagnóstico:
```bash
tail -f /var/log/v2ray/access.log    # Logs V2Ray
tail -f /var/log/syslog | grep GEOBLOCK  # Logs bloqueo
netstat -tlnp | grep -E "(80|2052|10000)"  # Puertos activos
```

## 🛠️ Solución de Problemas

### Proxy no funciona:
```bash
# Verificar servicios
systemctl status v2ray nginx

# Verificar configuración nginx
nginx -t

# Reiniciar todo
systemctl restart v2ray nginx
```

### Bloqueo geográfico muy restrictivo:
```bash
# Desactivar temporalmente
disable-geoblock

# Ver qué se está bloqueando
iptables -L GEOBLOCK -v -n

# Reactivar
apply-geoblock
```

### No puedo conectarme por SSH:
```bash
# El SSH (puerto 22) está protegido en las reglas
# Si tienes problemas, desde otro servidor:
ssh root@TU_IP -p 22
disable-geoblock
```

## 🔄 Mantenimiento

### Actualizaciones Automáticas:
- Las listas de países se actualizan automáticamente cada domingo a las 2 AM
- Los servicios se reinician automáticamente al boot

### Actualizaciones Manuales:
```bash
# Actualizar listas de países
setup-geoblock && apply-geoblock

# Actualizar V2Ray
bash <(curl -L https://raw.githubusercontent.com/v2fly/fhs-install-v2ray/master/install-release.sh)
systemctl restart v2ray
```

## 📊 Puertos Utilizados

- **80**: HTTP proxy + V2Ray WebSocket
- **2052**: Puerto alternativo (compatible Cloudflare)
- **443**: Reservado para futuro SSL
- **10000**: V2Ray interno
- **22**: SSH (siempre permitido)

## ⚠️ Notas Importantes

1. **Backup de configuración**: Guarda el UUID generado
2. **DNS**: Configura el dominio para apuntar al VPS
3. **Firewall**: Los scripts configuran UFW automáticamente
4. **Logs**: Se guardan en `/var/log/v2ray/`
5. **Persistencia**: Las reglas iptables se guardan automáticamente

## 🆘 Soporte

Si tienes problemas:
1. Verifica que estés en un país permitido
2. Revisa los logs con los comandos de diagnóstico
3. Usa `disable-geoblock` si necesitas acceso temporal
4. Reinicia los servicios con `systemctl restart v2ray nginx`

---

**¡Disfruta de tu proxy V2Ray protegido geográficamente!** 🎉
