#!/bin/bash

# ========================================
# INSTALADOR PROTECCIÓN GEOGRÁFICA
# Bloquea todo excepto LATAM + USA + Canadá + Alemania + <PERSON>landa
# ========================================

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo_step() {
    echo -e "${BLUE}[PASO]${NC} $1"
}

# Verificar si es root
if [[ $EUID -ne 0 ]]; then
   echo_error "Este script debe ejecutarse como root"
   exit 1
fi

echo_info "=== INSTALADOR PROTECCIÓN GEOGRÁFICA ==="
echo_info "Permitidos: LATAM + USA + Canadá + Alemania + Holanda"
echo_info "Bloqueados: Resto del mundo"
echo ""

# Paso 1: Instalar dependencias
echo_step "1/6 - Instalando dependencias..."
apt update
apt install -y wget iptables-persistent

# Paso 2: Crear directorio y descargar listas de IPs
echo_step "2/6 - Descargando listas de IPs por país..."
mkdir -p /etc/geoblock

# Países permitidos (códigos ISO en minúsculas)
ALLOWED_COUNTRIES=(
    # LATAM
    "ar" "bo" "br" "cl" "co" "cr" "cu" "do" "ec" "sv" "gt" "hn" "mx" 
    "ni" "pa" "py" "pe" "uy" "ve" "bz" "gy" "sr" "gf"
    # Norte América
    "us" "ca"
    # Europa permitida
    "de" "nl"
)

# Función para descargar rangos de IP por país
download_country_ips() {
    local country=$1
    echo_info "Descargando IPs para $country..."
    
    # Intentar primera fuente
    if wget -q -O "/etc/geoblock/${country}.txt" \
        "https://www.ipdeny.com/ipblocks/data/countries/${country}.zone"; then
        echo_info "✅ $country descargado desde ipdeny"
    # Intentar fuente alternativa
    elif wget -q -O "/etc/geoblock/${country}.txt" \
        "https://raw.githubusercontent.com/herrbischoff/country-ip-blocks/master/ipv4/${country}.cidr"; then
        echo_info "✅ $country descargado desde github"
    else
        echo_warn "❌ No se pudo descargar $country"
    fi
}

# Descargar listas de IPs para países permitidos
for country in "${ALLOWED_COUNTRIES[@]}"; do
    download_country_ips "$country"
done

echo_info "Listas descargadas: $(ls /etc/geoblock/ | wc -l) países"

# Paso 3: Crear script de configuración
echo_step "3/6 - Creando script de configuración..."

cat > /usr/local/bin/setup-geoblock << 'EOF'
#!/bin/bash
# Configuración de bloqueo geográfico para IPTV

echo "=== CONFIGURANDO BLOQUEO GEOGRÁFICO ==="

# Crear directorio para listas de IPs
mkdir -p /etc/geoblock

# Países permitidos (códigos ISO)
ALLOWED_COUNTRIES=(
    # LATAM
    "ar" "bo" "br" "cl" "co" "cr" "cu" "do" "ec" "sv" "gt" "hn" "mx" 
    "ni" "pa" "py" "pe" "uy" "ve" "bz" "gy" "sr" "gf"
    # Norte América
    "us" "ca"
    # Europa permitida
    "de" "nl"
)

# Función para descargar rangos de IP por país
download_country_ips() {
    local country=$1
    echo "Descargando IPs para $country..."
    
    if wget -q -O "/etc/geoblock/${country}.txt" \
        "https://www.ipdeny.com/ipblocks/data/countries/${country}.zone"; then
        echo "✅ $country descargado"
    elif wget -q -O "/etc/geoblock/${country}.txt" \
        "https://raw.githubusercontent.com/herrbischoff/country-ip-blocks/master/ipv4/${country}.cidr"; then
        echo "✅ $country descargado (fuente alternativa)"
    else
        echo "❌ Error descargando $country"
    fi
}

# Descargar listas de IPs para países permitidos
for country in "${ALLOWED_COUNTRIES[@]}"; do
    download_country_ips "$country"
done

echo "✅ Listas de IPs actualizadas"
ls -la /etc/geoblock/ | grep -v "^total" | wc -l
EOF

chmod +x /usr/local/bin/setup-geoblock

# Paso 4: Crear script de aplicación de reglas
echo_step "4/6 - Creando script de aplicación de reglas..."

cat > /usr/local/bin/apply-geoblock << 'EOF'
#!/bin/bash
# Aplicar reglas de bloqueo geográfico

echo "=== APLICANDO REGLAS DE BLOQUEO GEOGRÁFICO ==="

# Limpiar reglas existentes de geoblock
iptables -F GEOBLOCK 2>/dev/null || true
iptables -X GEOBLOCK 2>/dev/null || true

# Crear nueva cadena GEOBLOCK
iptables -N GEOBLOCK

# Permitir tráfico local y establecido
iptables -A GEOBLOCK -i lo -j ACCEPT
iptables -A GEOBLOCK -m state --state ESTABLISHED,RELATED -j ACCEPT

# Permitir SSH (puerto 22) desde cualquier lugar para no bloquearte
iptables -A GEOBLOCK -p tcp --dport 22 -j ACCEPT

# Permitir países específicos
ALLOWED_COUNTRIES=("ar" "bo" "br" "cl" "co" "cr" "cu" "do" "ec" "sv" "gt" "hn" "mx" "ni" "pa" "py" "pe" "uy" "ve" "bz" "gy" "sr" "gf" "us" "ca" "de" "nl")

RULES_ADDED=0
for country in "${ALLOWED_COUNTRIES[@]}"; do
    if [ -f "/etc/geoblock/${country}.txt" ] && [ -s "/etc/geoblock/${country}.txt" ]; then
        echo "Permitiendo $country..."
        while read -r ip; do
            if [ -n "$ip" ] && [[ "$ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+(/[0-9]+)?$ ]]; then
                iptables -A GEOBLOCK -s "$ip" -j ACCEPT
                ((RULES_ADDED++))
            fi
        done < "/etc/geoblock/${country}.txt"
    else
        echo "⚠️  Archivo vacío o no encontrado: $country"
    fi
done

# Log de conexiones bloqueadas (opcional, comentar si no quieres logs)
# iptables -A GEOBLOCK -j LOG --log-prefix "GEOBLOCK: " --log-level 4

# Bloquear todo lo demás
iptables -A GEOBLOCK -j DROP

# Remover reglas existentes si existen
iptables -D INPUT -p tcp --dport 80 -j GEOBLOCK 2>/dev/null || true
iptables -D INPUT -p tcp --dport 2052 -j GEOBLOCK 2>/dev/null || true
iptables -D INPUT -p tcp --dport 443 -j GEOBLOCK 2>/dev/null || true

# Aplicar la cadena a los puertos de tu servicio
iptables -I INPUT -p tcp --dport 80 -j GEOBLOCK
iptables -I INPUT -p tcp --dport 2052 -j GEOBLOCK
iptables -I INPUT -p tcp --dport 443 -j GEOBLOCK

echo "✅ Reglas de bloqueo geográfico aplicadas"
echo "📊 Países permitidos: ${#ALLOWED_COUNTRIES[@]}"
echo "📋 Reglas IP agregadas: $RULES_ADDED"
echo "🚫 Resto del mundo: BLOQUEADO"
EOF

chmod +x /usr/local/bin/apply-geoblock

# Paso 5: Crear script de monitoreo
echo_step "5/6 - Creando script de monitoreo..."

cat > /usr/local/bin/monitor-geoblock << 'EOF'
#!/bin/bash
# Monitorear conexiones bloqueadas

echo "=== MONITOREO DE BLOQUEO GEOGRÁFICO ==="
echo ""
echo "📊 Conexiones actuales por puerto:"
echo "Puerto 80: $(netstat -an 2>/dev/null | grep :80 | grep ESTABLISHED | wc -l)"
echo "Puerto 2052: $(netstat -an 2>/dev/null | grep :2052 | grep ESTABLISHED | wc -l)"
echo "Puerto 443: $(netstat -an 2>/dev/null | grep :443 | grep ESTABLISHED | wc -l)"
echo ""
echo "🚫 Estadísticas de bloqueo:"
BLOCKED_PACKETS=$(iptables -L GEOBLOCK -v -n 2>/dev/null | grep DROP | awk '{sum+=$1} END {print sum+0}')
echo "Paquetes bloqueados: $BLOCKED_PACKETS"
echo ""
echo "📈 Reglas activas en GEOBLOCK:"
ACTIVE_RULES=$(iptables -L GEOBLOCK -n 2>/dev/null | grep -c ACCEPT)
echo "Reglas de países permitidos: $ACTIVE_RULES"
echo ""
echo "🌍 Países configurados:"
echo "Archivos descargados: $(ls /etc/geoblock/ 2>/dev/null | wc -l)"
echo ""
echo "🔧 Estado de servicios:"
echo "V2Ray: $(systemctl is-active v2ray 2>/dev/null || echo 'no instalado')"
echo "Nginx: $(systemctl is-active nginx 2>/dev/null || echo 'no instalado')"
EOF

chmod +x /usr/local/bin/monitor-geoblock

# Crear script de desactivación temporal
cat > /usr/local/bin/disable-geoblock << 'EOF'
#!/bin/bash
echo "=== DESACTIVANDO BLOQUEO GEOGRÁFICO TEMPORALMENTE ==="
iptables -D INPUT -p tcp --dport 80 -j GEOBLOCK 2>/dev/null || true
iptables -D INPUT -p tcp --dport 2052 -j GEOBLOCK 2>/dev/null || true
iptables -D INPUT -p tcp --dport 443 -j GEOBLOCK 2>/dev/null || true
echo "✅ Bloqueo geográfico desactivado"
echo "💡 Para reactivar: apply-geoblock"
EOF

chmod +x /usr/local/bin/disable-geoblock

# Paso 6: Configurar servicios y automatización
echo_step "6/6 - Configurando servicios automáticos..."

# Crear servicio systemd
cat > /etc/systemd/system/geoblock.service << 'EOF'
[Unit]
Description=Geographic IP Blocking
After=network.target

[Service]
Type=oneshot
ExecStart=/usr/local/bin/apply-geoblock
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

# Habilitar servicio
systemctl daemon-reload
systemctl enable geoblock

# Agregar cron para actualizar listas semanalmente
CRON_JOB="0 2 * * 0 /usr/local/bin/setup-geoblock && /usr/local/bin/apply-geoblock && iptables-save > /etc/iptables/rules.v4"
(crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -

# Aplicar reglas por primera vez
echo_step "Aplicando reglas iniciales..."
/usr/local/bin/apply-geoblock

# Guardar reglas permanentemente
iptables-save > /etc/iptables/rules.v4

echo ""
echo_info "=== PROTECCIÓN GEOGRÁFICA INSTALADA ==="
echo ""
echo "🌍 Países permitidos:"
echo "  LATAM: Argentina, Bolivia, Brasil, Chile, Colombia, Costa Rica, Cuba,"
echo "         República Dominicana, Ecuador, El Salvador, Guatemala, Honduras,"
echo "         México, Nicaragua, Panamá, Paraguay, Perú, Uruguay, Venezuela,"
echo "         Belice, Guyana, Surinam, Guayana Francesa"
echo "  Norte América: Estados Unidos, Canadá"
echo "  Europa: Alemania, Holanda"
echo ""
echo "🚫 Bloqueados: Resto del mundo"
echo ""
echo "🔧 Comandos útiles:"
echo "  - monitor-geoblock (ver estado y estadísticas)"
echo "  - apply-geoblock (aplicar/recargar reglas)"
echo "  - setup-geoblock (actualizar listas de países)"
echo "  - disable-geoblock (desactivar temporalmente)"
echo ""
echo "📊 Estado actual:"
/usr/local/bin/monitor-geoblock
echo ""
echo_warn "IMPORTANTE: Las reglas se actualizan automáticamente cada domingo a las 2 AM"
echo_info "¡Protección geográfica instalada exitosamente!"
