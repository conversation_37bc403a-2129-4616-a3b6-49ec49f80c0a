#!/bin/bash

# ========================================
# INSTALADOR AUTOMÁTICO V2RAY PROXY
# Para proteger servidor <PERSON><PERSON>/IPTV
# ========================================

set -e

# Variables de configuración
TARGET_IP="**************"  # IP del servidor Xu<PERSON> (CAMBIAR SEGÚN NECESITES)
CDN_DOMAIN="cdn.rgstreamy.site"  # Dominio CDN (CAMBIAR SEGÚN NECESITES)
V2RAY_PORT="10000"
HTTP_PORT="80"
HTTPS_PORT="443"
CF_PORT="2052"

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

echo_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo_step() {
    echo -e "${BLUE}[PASO]${NC} $1"
}

# Verificar si es root
if [[ $EUID -ne 0 ]]; then
   echo_error "Este script debe ejecutarse como root"
   exit 1
fi

echo_info "=== INSTALADOR V2RAY PROXY PARA IPTV ==="
echo_info "IP objetivo: $TARGET_IP"
echo_info "Dominio CDN: $CDN_DOMAIN"
echo ""

# Paso 1: Actualizar sistema
echo_step "1/8 - Actualizando sistema..."
apt update && apt upgrade -y

# Paso 2: Instalar dependencias
echo_step "2/8 - Instalando dependencias..."
apt install -y curl wget unzip nginx ufw net-tools

# Paso 3: Descargar e instalar V2Ray
echo_step "3/8 - Instalando V2Ray..."
bash <(curl -L https://raw.githubusercontent.com/v2fly/fhs-install-v2ray/master/install-release.sh)

# Paso 4: Generar UUID y configurar V2Ray
echo_step "4/8 - Configurando V2Ray..."
UUID=$(cat /proc/sys/kernel/random/uuid)
echo_info "UUID generado: $UUID"

mkdir -p /usr/local/etc/v2ray
mkdir -p /var/log/v2ray
chown nobody:nogroup /var/log/v2ray

cat > /usr/local/etc/v2ray/config.json << EOF
{
  "log": {
    "access": "/var/log/v2ray/access.log",
    "error": "/var/log/v2ray/error.log",
    "loglevel": "warning"
  },
  "inbounds": [
    {
      "port": $V2RAY_PORT,
      "protocol": "vmess",
      "settings": {
        "clients": [
          {
            "id": "$UUID",
            "alterId": 0
          }
        ]
      },
      "streamSettings": {
        "network": "ws",
        "wsSettings": {
          "path": "/v2ray"
        }
      }
    }
  ],
  "outbounds": [
    {
      "protocol": "freedom",
      "settings": {},
      "tag": "direct"
    },
    {
      "protocol": "blackhole",
      "settings": {},
      "tag": "blocked"
    }
  ],
  "routing": {
    "rules": [
      {
        "type": "field",
        "ip": ["geoip:private"],
        "outboundTag": "blocked"
      }
    ]
  }
}
EOF

# Paso 5: Configurar Nginx
echo_step "5/8 - Configurando Nginx..."
rm -f /etc/nginx/sites-enabled/*

cat > /etc/nginx/sites-available/v2ray-proxy << EOF
# Configuración HTTP en puerto 80
server {
    listen 80;
    server_name _ $CDN_DOMAIN;

    # Proxy V2Ray WebSocket en /v2ray
    location /v2ray {
        proxy_redirect off;
        proxy_pass http://127.0.0.1:$V2RAY_PORT;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }

    # Proxy principal hacia la IP objetivo
    location / {
        proxy_pass http://$TARGET_IP;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # Headers para ocultar el proxy
        proxy_hide_header X-Powered-By;
        proxy_hide_header Server;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}

# Configuración para puerto 2052 (Cloudflare compatible)
server {
    listen $CF_PORT;
    server_name _ $CDN_DOMAIN;

    # Proxy V2Ray WebSocket en /v2ray
    location /v2ray {
        proxy_redirect off;
        proxy_pass http://127.0.0.1:$V2RAY_PORT;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }

    # Proxy principal hacia la IP objetivo
    location / {
        proxy_pass http://$TARGET_IP;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # Headers para ocultar el proxy
        proxy_hide_header X-Powered-By;
        proxy_hide_header Server;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}
EOF

ln -sf /etc/nginx/sites-available/v2ray-proxy /etc/nginx/sites-enabled/

# Paso 6: Configurar firewall
echo_step "6/8 - Configurando firewall..."
ufw --force enable
ufw allow ssh
ufw allow $HTTP_PORT
ufw allow $HTTPS_PORT
ufw allow $CF_PORT
ufw allow $V2RAY_PORT

# Paso 7: Crear scripts de utilidad
echo_step "7/8 - Creando scripts de utilidad..."

cat > /usr/local/bin/v2ray-status << 'EOF'
#!/bin/bash
echo "=== ESTADO DEL PROXY V2RAY ==="
echo ""
echo "🔧 Servicios:"
echo "V2Ray: $(systemctl is-active v2ray)"
echo "Nginx: $(systemctl is-active nginx)"
echo ""
echo "🌐 Puertos escuchando:"
netstat -tlnp | grep -E "(80|2052|10000)" | while read line; do
    echo "  $line"
done
echo ""
echo "📊 Configuración V2Ray:"
echo "UUID: $(grep -o '"id": "[^"]*"' /usr/local/etc/v2ray/config.json | cut -d'"' -f4)"
echo "Puerto V2Ray: 10000"
echo "Ruta WebSocket: /v2ray"
echo ""
echo "🎯 Proxy destino: TARGET_IP_PLACEHOLDER"
echo ""
echo "📱 Puertos para clientes:"
echo "  - Puerto 80 (HTTP)"
echo "  - Puerto 2052 (Cloudflare compatible)"
echo ""
echo "🔗 URLs de prueba:"
echo "  - http://$(curl -s ifconfig.me)/"
echo "  - http://$(curl -s ifconfig.me):2052/"
echo ""
echo "📝 Últimas líneas del log V2Ray:"
tail -n 3 /var/log/v2ray/access.log 2>/dev/null || echo "  No hay logs aún"
EOF

sed -i "s/TARGET_IP_PLACEHOLDER/$TARGET_IP/g" /usr/local/bin/v2ray-status
chmod +x /usr/local/bin/v2ray-status

cat > /usr/local/bin/test-xui-proxy << 'EOF'
#!/bin/bash
echo "=== PRUEBA DE PROXY XUI ==="
echo ""
TARGET_IP="TARGET_IP_PLACEHOLDER"
echo "🎯 Servidor original ($TARGET_IP):"
curl -I http://$TARGET_IP/panel 2>/dev/null | head -3
echo ""
echo "🔄 A través del proxy puerto 80:"
curl -I http://localhost/panel 2>/dev/null | head -3
echo ""
echo "🔄 A través del proxy puerto 2052:"
curl -I http://localhost:2052/panel 2>/dev/null | head -3
echo ""
echo "📊 Comparación de headers Server:"
echo "Original: $(curl -I http://$TARGET_IP/panel 2>/dev/null | grep Server:)"
echo "Proxy 80: $(curl -I http://localhost/panel 2>/dev/null | grep Server:)"
echo "Proxy 2052: $(curl -I http://localhost:2052/panel 2>/dev/null | grep Server:)"
echo ""
echo "✅ Si ves headers diferentes, el proxy está funcionando correctamente"
EOF

sed -i "s/TARGET_IP_PLACEHOLDER/$TARGET_IP/g" /usr/local/bin/test-xui-proxy
chmod +x /usr/local/bin/test-xui-proxy

# Paso 8: Iniciar servicios
echo_step "8/8 - Iniciando servicios..."
systemctl enable v2ray
systemctl start v2ray
systemctl enable nginx
systemctl restart nginx

# Verificar configuración
nginx -t

# Mostrar información final
echo ""
echo_info "=== INSTALACIÓN COMPLETADA ==="
echo ""
echo "🎯 Información del servidor:"
echo "IP del VPS: $(curl -s ifconfig.me)"
echo "IP objetivo: $TARGET_IP"
echo "Dominio: $CDN_DOMAIN"
echo ""
echo "📊 Configuración V2Ray:"
echo "UUID: $UUID"
echo "Puerto: $V2RAY_PORT"
echo "Ruta WebSocket: /v2ray"
echo ""
echo "🌐 URLs para clientes:"
echo "  - http://$(curl -s ifconfig.me)/"
echo "  - http://$(curl -s ifconfig.me):$CF_PORT/"
echo "  - http://$CDN_DOMAIN/ (si DNS configurado)"
echo ""
echo "🔧 Comandos útiles:"
echo "  - v2ray-status (ver estado)"
echo "  - test-xui-proxy (probar proxy)"
echo "  - systemctl restart v2ray nginx (reiniciar)"
echo ""
echo_warn "IMPORTANTE: Configura el DNS de $CDN_DOMAIN para apuntar a $(curl -s ifconfig.me)"
echo_info "¡Proxy V2Ray instalado exitosamente!"
